import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';

import { loginBranchID, loginUserID, bearerToken } from '../../globals';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';
import { fetchCurrentCashBalance } from '../../apiHandling/FinanceAPI/branchTransferAPIs/currentCashBalanceAPI';
import { fetchExpenseGroups, fetchExpenseHeads } from '../../apiHandling/FinanceAPI/PaymentAPI/expenseAPI';
import { fetchBudgetAmount } from '../../apiHandling/FinanceAPI/PaymentAPI/budgetAmountAPI';
import { fetchPaymentMethods } from '../../apiHandling/FinanceAPI/PaymentAPI/paymentMethodsAPI';

const PaymentScreen = () => {

  const [businessDate, setBusinessDate] = useState('');
  const [currentBalance, setCurrentBalance] = useState('');

  const [expenseGroupList, setExpenseGroupList] = useState([]);
  const [expenseHeadList, setExpenseHeadList] = useState([]);
  const [selectedExpenseGroup, setSelectedExpenseGroup] = useState(null);
  const [selectedExpenseHead, setSelectedExpenseHead] = useState(null);

  const [budgetAmount, setBudgetAmount] = useState('');
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);

  const [remarks, setRemarks] = useState('');
  const [billMonth, setBillMonth] = useState('');
  const [pumpOdoReading, setPumpOdoReading] = useState('');
  const [amount, setAmount] = useState('');
  const [tableData, setTableData] = useState([]);




  useEffect(() => {
    const getBusinessDate = async () => {
      const date = await fetchBusinessDay(bearerToken, loginBranchID);
      setBusinessDate(date);
    };

    getBusinessDate();
  }, []);

  useEffect(() => {
    const getBusinessDateAndBalance = async () => {
      const businessDateFormatted = await fetchBusinessDay(bearerToken, loginBranchID);
      setBusinessDate(businessDateFormatted);
      const [day, month, year] = businessDateFormatted.split('-');
      const businessDateCode = `${year}${month}${day}`;
      const balance = await fetchCurrentCashBalance(bearerToken, loginBranchID, businessDateCode);
      setCurrentBalance(balance);
    };

    getBusinessDateAndBalance();
  }, []);

  useEffect(() => {
    const loadGroups = async () => {
      const groups = await fetchExpenseGroups(bearerToken, loginBranchID);
      setExpenseGroupList(groups);
    };
    loadGroups();
  }, []);

  useEffect(() => {
    const loadHeads = async () => {
      if (selectedExpenseGroup) {
        const heads = await fetchExpenseHeads(bearerToken, loginBranchID, selectedExpenseGroup);
        setExpenseHeadList(heads);
      } else {
        setExpenseHeadList([]);
      }
    };
    loadHeads();
  }, [selectedExpenseGroup]);

  useEffect(() => {
    const loadBudgetAmount = async () => {
      if (selectedExpenseGroup && businessDate) {
        const [day, month, year] = businessDate.split('-');
        const businessDateCode = `${year}${month}${day}`;
        const amount = await fetchBudgetAmount(bearerToken, loginBranchID, selectedExpenseGroup, businessDateCode);
        setBudgetAmount(amount.toString());
      } else {
        setBudgetAmount('');
      }
    };

    loadBudgetAmount();
  }, [selectedExpenseGroup, businessDate]);

  useEffect(() => {
    const loadPaymentMethods = async () => {
      const methods = await fetchPaymentMethods(bearerToken, loginBranchID);
      setPaymentMethods(methods);
    };
    loadPaymentMethods();
  }, []);

  const handleAddRow = () => {
    const newRow = {
      id: Date.now(), // Unique identifier
      lineNumber: (tableData.length + 1).toString().padStart(3, '0'),
      expenseHead: selectedExpenseHead?.label || '',
      expenseGroup: selectedExpenseGroup?.label || '',
      remark: remarks,
      billMonth,
      pumpOdoReading,
      paymentMode: selectedPaymentMethod?.label || '',
      amount,
      isChecked: false,
    };

    setTableData([...tableData, newRow]);

    // Clear fields
    setSelectedExpenseHead(null);
    setSelectedExpenseGroup(null);
    setSelectedPaymentMethod(null);
    setRemarks('');
    setBillMonth('');
    setPumpOdoReading('');
    setAmount('');
  };




  return (
    <View style={styles.container}>
      <Navbar />

      {/* Scroll Options */}
      <View style={styles.scrollOptions_container}>
        <View style={styles.scrollOptions_row}>
          {/* Page Title */}
          <TouchableOpacity style={styles.scrollOptions_backContainer}>
            <Text style={styles.scrollOptions_screenTitle}>Payment</Text>
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.scrollOptions_buttonsContainer}>
            {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
              let buttonStyle = [styles.scrollOptions_button];

              if (option === 'Cancel') {
                buttonStyle.push({
                  backgroundColor: '#FF3333',
                });
              } else if (option === 'Save') {
                buttonStyle.push({
                  backgroundColor: '#02A515',
                });
              } else {
                buttonStyle.push({
                  backgroundColor: '#DEDDDD',
                });
              }

              return (
                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                  <TouchableOpacity
                    style={buttonStyle}
                  >
                    <Text style={[
                      styles.scrollOptions_buttonText,
                      { color: 'black' },
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </View>
      </View>

      {/* Main Content ScrollView */}
      <ScrollView style={{ flex: 1 }}>
        <View style={styles.paymentContainer}>
          {/* Row 1: Document No., Business Date, Cash on hand */}
          <View style={styles.inputRow}>
            <TextInput
              style={styles.textField}
              placeholder="Document Nos."
            />
            <TextInput
              style={styles.textField}
              placeholder="Business Date"
              value={businessDate}
              onChangeText={setBusinessDate}
            />
            <TextInput
              style={styles.textField}
              placeholder="Cash on hand"
              editable={false}
              value={currentBalance}
              onChangeText={setCurrentBalance}
              keyboardType="numeric"

            />
          </View>

          {/* Row 2: Expenses Group, Expenses Head */}
          <View style={styles.inputRow}>
            <View style={styles.dropdownContainer}>
              <Dropdown
                data={expenseGroupList}
                labelField="label"
                valueField="value"
                value={selectedExpenseGroup}
                onChange={item => setSelectedExpenseGroup(item.value)}
                placeholder="Expenses Group"
                style={styles.dropdown}
              />
            </View>
            <View style={styles.dropdownContainer}>
              <Dropdown
                data={expenseHeadList}
                labelField="label"
                valueField="value"
                value={selectedExpenseHead}
                onChange={item => setSelectedExpenseHead(item.value)}
                placeholder="Expenses Head"
                style={styles.dropdown}
              />
            </View>
          </View>

          {/* Row 3: Budget Amount, Remark */}
          <View style={styles.inputRow}>
            <TextInput
              style={styles.textField}
              placeholder="Budget Amount"
              keyboardType="numeric"
              value={budgetAmount}
              editable={false}
            />

            <TextInput
              style={styles.textField}
              placeholder="Remark"
            />
          </View>

          {/* Row 4: Payment Mode, Amount, Units Consumed */}
          <View style={styles.inputRow}>
            <View style={styles.dropdownContainer}>
              <Dropdown
                data={paymentMethods}
                labelField="label"
                valueField="value"
                placeholder="Payment Mode"
                style={styles.dropdown}
                value={selectedPaymentMethod}
                onChange={(item) => setSelectedPaymentMethod(item.value)}
              />

            </View>
            <TextInput
              style={styles.textField}
              placeholder="Amount"
              keyboardType="numeric"
            />
            <TextInput
              style={styles.textField}
              placeholder="Units Consumed"
              keyboardType="numeric"
            />
          </View>

          {/* Row 5: Add and Clear Buttons */}
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleAddRow}
            >

              <Text style={styles.buttonText}>Add</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => {
                setSelectedExpenseHead(null);
                setSelectedExpenseGroup(null);
                setSelectedPaymentMethod(null);
                setRemarks('');
                setBillMonth('');
                setPumpOdoReading('');
                setAmount('');
              }}
            >
              <Text style={styles.buttonText}>Clear</Text>
            </TouchableOpacity>
          </View>

          {/* Row 6: Table */}
          <View style={styles.tableContainer}>
            <DataTable>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title style={styles.checkboxColumn}></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Line No</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Expense head</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Expense group</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Remarks</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Bill Month</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Pump odo reading</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Payment mode</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Amount</Text></DataTable.Title>
              </DataTable.Header>

              {tableData.map((row, index) => (
                <DataTable.Row key={row.id}>
                  <DataTable.Cell style={styles.checkboxColumn}>
                    {/* checkbox if needed */}
                  </DataTable.Cell>
                  <DataTable.Cell>{row.lineNumber}</DataTable.Cell>
                  <DataTable.Cell>{row.expenseHead}</DataTable.Cell>
                  <DataTable.Cell>{row.expenseGroup}</DataTable.Cell>
                  <DataTable.Cell>{row.remark}</DataTable.Cell>
                  <DataTable.Cell>{row.billMonth}</DataTable.Cell>
                  <DataTable.Cell>{row.pumpOdoReading}</DataTable.Cell>
                  <DataTable.Cell>{row.paymentMode}</DataTable.Cell>
                  <DataTable.Cell>{row.amount}</DataTable.Cell>
                </DataTable.Row>
              ))}

            </DataTable>
          </View>

          {/* Row 7: Delete Button and Total Amount */}
          <View style={styles.finalRow}>
            <TouchableOpacity
              style={styles.deleteButton}
            >
              <Text style={styles.buttonText}>Delete Selected Row</Text>
            </TouchableOpacity>

            <TextInput
              style={styles.totalField}
              placeholder="Amount"
              value="40000"
              editable={false}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  // Scroll Options Styles (keeping the same from original)
  scrollOptions_container: {
    backgroundColor: '#E6E6E6',
    paddingVertical: 8,
    marginTop: 0,
  },
  scrollOptions_row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  scrollOptions_backContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollOptions_screenTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'black',
  },
  scrollOptions_buttonsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  scrollOptions_buttonWrapper: {
    width: '22%',
    marginHorizontal: 5,
  },
  scrollOptions_button: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  scrollOptions_buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Payment Screen Styles
  paymentContainer: {
    backgroundColor: '#FFFFFF',
    padding: 12,
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  textField: {
    flex: 1,
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    marginHorizontal: 4,
  },
  dropdownContainer: {
    flex: 1,
    marginHorizontal: 4,
  },
  dropdown: {
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 10,
  },
  addButton: {
    flex: 1,
    height: 50,
    backgroundColor: '#02096A',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  clearButton: {
    flex: 1,
    height: 50,
    backgroundColor: '#02096A',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  tableContainer: {
    marginVertical: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    backgroundColor: 'white',
  },
  tableHeader: {
    backgroundColor: '#02096A',
  },
  tableHeaderText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  checkboxColumn: {
    maxWidth: 40,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#02096A',
    borderRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#02096A',
  },
  checkmark: {
    color: 'white',
    fontSize: 12,
  },
  finalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  deleteButton: {
    height: 50,
    backgroundColor: 'red',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    flex: 2,
    marginRight: 10,
  },
  totalField: {
    flex: 1,
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    textAlign: 'right',
  }
});

export default PaymentScreen;